#!/usr/bin/env python3
"""
Data processing script for Maybank Excel data
Processes Raw Data, Exchange rates, and generates Report Format output
"""

import pandas as pd
import numpy as np
import sys
import warnings
warnings.filterwarnings('ignore')

def load_excel_data(filename='data.xlsx'):
    """Load all sheets from Excel file"""
    try:
        excel_file = pd.ExcelFile(filename)
        sheets = {}
        
        # Load Raw Data (rows 4-11, which are index 3-10 in 0-based indexing)
        raw_data = pd.read_excel(filename, sheet_name='Raw Data', header=None)
        sheets['raw_data'] = raw_data
        
        # Load Exchange rates
        exchange_rates = pd.read_excel(filename, sheet_name='Exchange rate', header=0)
        sheets['exchange_rates'] = exchange_rates
        
        # Load Report Format template
        report_format = pd.read_excel(filename, sheet_name='Report Format', header=None)
        sheets['report_format'] = report_format
        
        # Load Expected result for reference
        expected_result = pd.read_excel(filename, sheet_name='Expected result', header=None)
        sheets['expected_result'] = expected_result
        
        return sheets
        
    except Exception as e:
        print(f"Error loading Excel file: {e}")
        return None

def extract_raw_data(raw_data_df):
    """Extract data from rows 4-11 (index 3-10) of Raw Data sheet"""
    # Extract the relevant rows (4-11 in Excel = index 3-10 in pandas)
    data_rows = raw_data_df.iloc[3:11].copy()
    
    # Get the column headers from row 3 (index 2)
    headers = raw_data_df.iloc[2].tolist()
    
    print("Raw data structure:")
    print("Headers:", headers)
    print("\nData rows:")
    print(data_rows)
    
    return data_rows, headers

def get_exchange_rate(currency, exchange_rates_df):
    """Get USD exchange rate for a given currency"""
    if currency == 'USD':
        return 1.0
    
    # Find the exchange rate for the currency
    rate_row = exchange_rates_df[exchange_rates_df['Currency'] == currency]
    if not rate_row.empty:
        return rate_row['Exchange rate to convert to USD'].iloc[0]
    else:
        print(f"Warning: Exchange rate not found for {currency}, using 1.0")
        return 1.0

def identify_currency_from_data(data_rows):
    """Identify currency from the bond names or data"""
    # Look for currency indicators in the bond names
    bond_names = []
    for idx, row in data_rows.iterrows():
        if pd.notna(row.iloc[0]) and 'Bond' in str(row.iloc[0]):
            bond_names.append(str(row.iloc[0]))
    
    print("Bond names found:", bond_names)
    
    # For Bond US#234, currency is USD
    # For Bond #123, we need to determine the currency (likely SGD based on context)
    currencies = {}
    for bond in bond_names:
        if 'US' in bond:
            currencies[bond] = 'USD'
        else:
            # Default to SGD for other bonds (can be adjusted based on actual data)
            currencies[bond] = 'SGD'
    
    return currencies

def process_delta_data(data_rows, headers, currencies, exchange_rates_df):
    """Extract and process Delta rows, convert to USD"""
    delta_data = {}
    current_bond = None
    
    for idx, row in data_rows.iterrows():
        cell_value = str(row.iloc[0]).strip()
        
        # Check if this is a bond header row
        if 'Bond' in cell_value and cell_value != 'nan':
            current_bond = cell_value
            print(f"Processing bond: {current_bond}")
            continue
            
        # Check if this is a Delta row
        if cell_value == 'Delta' and current_bond:
            print(f"Found Delta row for {current_bond}")
            
            # Get currency for this bond
            currency = currencies.get(current_bond, 'SGD')
            exchange_rate = get_exchange_rate(currency, exchange_rates_df)
            
            print(f"Currency: {currency}, Exchange rate: {exchange_rate}")
            
            # Extract delta values and convert to USD
            delta_values = []
            for i in range(1, len(row)):
                value = row.iloc[i]
                if pd.notna(value) and str(value) != '---' and str(value) != 'nan':
                    try:
                        usd_value = float(value) * exchange_rate
                        delta_values.append(usd_value)
                        print(f"  {headers[i]}: {value} {currency} -> {usd_value} USD")
                    except (ValueError, TypeError):
                        delta_values.append(0)
                else:
                    delta_values.append(0)
            
            delta_data[current_bond] = {
                'currency': currency,
                'exchange_rate': exchange_rate,
                'values': delta_values,
                'headers': headers[1:]  # Skip the first column (Greeks)
            }
    
    return delta_data

def create_report_format(delta_data, report_format_df):
    """Create the final report in the required format"""
    # Create a copy of the report format template
    result_df = report_format_df.copy()

    # Find the data section (starts after "Currency Tenors Bonds" row)
    data_start_row = None
    for idx, row in result_df.iterrows():
        if 'Currency' in str(row.iloc[0]) and 'Tenors' in str(row.iloc[1]):
            data_start_row = idx + 1
            break

    if data_start_row is None:
        print("Could not find data start row in report format")
        return result_df

    print(f"Data starts at row {data_start_row}")

    # Map tenors from raw data to report format
    tenor_mapping = {
        'OVERNIGHT': 'o/n',
        '1 MONTH': '1m',
        '2 MONTHS': '2m',
        '3 MONTHS': '3m',
        '6 MONTHS': '6m',
        '9 MONTHS': '9m',
        '1 YEAR': '1y',
        '18 MONTHS': '18m',
        '2 YEARS': '2y',
        '3 YEARS': '3y',
        '4 YEARS': '4y',
        '5 YEARS': '5y',
        '7 YEARS': '7y',
        '10 YEARS': '10y',
        '15 YEARS': '15y',
        '20 YEARS': '20y'
    }

    # Based on expected result, we only process USD bonds (Bond US#234)
    # Process only USD currency bonds
    for bond_name, bond_data in delta_data.items():
        if bond_data['currency'] != 'USD':
            continue  # Skip non-USD bonds for this report

        print(f"\nProcessing {bond_name} for report:")

        headers = bond_data['headers']
        values = bond_data['values']

        # Get the total from the "Total" column (last column in the raw data)
        # This should be the value from the Total column, not a sum of individual values
        total_value = values[-1] if values else 0  # Last value is the Total column

        # Map values to report format rows (USD section only)
        for i, header in enumerate(headers):
            if i < len(values) and header in tenor_mapping:
                report_tenor = tenor_mapping[header]
                value = values[i]

                # Find the row in USD section that matches this tenor
                usd_section_found = False
                for idx in range(data_start_row, len(result_df)):
                    if idx < len(result_df):
                        # Check if we're in USD section
                        if str(result_df.iloc[idx, 0]).strip() == 'USD':
                            usd_section_found = True
                        elif usd_section_found and str(result_df.iloc[idx, 0]).strip() != 'NaN' and pd.notna(result_df.iloc[idx, 0]):
                            # We've moved to another currency section
                            break

                        # If we're in USD section and tenor matches
                        if usd_section_found and str(result_df.iloc[idx, 1]).strip() == report_tenor:
                            # Format value: if it's 0.0, show as 0
                            formatted_value = 0 if value == 0.0 else value
                            result_df.iloc[idx, 2] = formatted_value
                            print(f"  {report_tenor}: {formatted_value}")
                            break

        # Add total to the Total row in USD section
        usd_section_found = False
        for idx in range(data_start_row, len(result_df)):
            if idx < len(result_df):
                if str(result_df.iloc[idx, 0]).strip() == 'USD':
                    usd_section_found = True
                elif usd_section_found and str(result_df.iloc[idx, 0]).strip() != 'NaN' and pd.notna(result_df.iloc[idx, 0]):
                    break

                if usd_section_found and str(result_df.iloc[idx, 1]).strip() == 'Total':
                    result_df.iloc[idx, 2] = total_value
                    print(f"  Total: {total_value}")

                    # Calculate utilisation percentage
                    if idx + 2 < len(result_df) and str(result_df.iloc[idx + 2, 1]).strip() == 'Utilisation (%)':
                        limit_value = result_df.iloc[idx + 1, 2]  # Limit row
                        if pd.notna(limit_value) and limit_value != 0:
                            utilisation = (total_value / limit_value)  # Don't multiply by 100, it's already a ratio
                            result_df.iloc[idx + 2, 2] = round(utilisation, 6)
                            print(f"  Utilisation: {utilisation:.6f}")
                    break

    return result_df

def main():
    """Main processing function"""
    print("Loading Excel data...")
    sheets = load_excel_data()
    
    if sheets is None:
        return
    
    print("\n" + "="*50)
    print("STEP 1: Extract Raw Data (rows 4-11)")
    print("="*50)
    
    # Extract raw data from rows 4-11
    data_rows, headers = extract_raw_data(sheets['raw_data'])
    
    print("\n" + "="*50)
    print("STEP 2: Identify Currencies")
    print("="*50)
    
    # Identify currencies for each bond
    currencies = identify_currency_from_data(data_rows)
    print("Currencies identified:", currencies)
    
    print("\n" + "="*50)
    print("STEP 3: Process Delta Data and Convert to USD")
    print("="*50)
    
    # Process Delta rows and convert to USD
    delta_data = process_delta_data(data_rows, headers, currencies, sheets['exchange_rates'])
    
    print("\n" + "="*50)
    print("STEP 4: Create Report Format")
    print("="*50)
    
    # Create the final report
    result_df = create_report_format(delta_data, sheets['report_format'])
    
    print("\n" + "="*50)
    print("FINAL RESULT")
    print("="*50)
    
    # Display the result
    print(result_df.to_string(index=False))
    
    # Save to Excel
    result_df.to_excel('processed_report.xlsx', index=False, header=False)
    print(f"\nReport saved to 'processed_report.xlsx'")
    
    print("\n" + "="*50)
    print("EXPECTED RESULT (for comparison)")
    print("="*50)
    print(sheets['expected_result'].to_string(index=False))

if __name__ == "__main__":
    main()
